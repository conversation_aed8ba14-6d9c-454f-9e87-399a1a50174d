#!/usr/bin/env node

/**
 * Slash Commands Performance Benchmark
 * 
 * This script benchmarks the custom slash commands feature by measuring:
 * - CLI startup time with custom commands enabled
 * - Custom command discovery performance
 * - Command execution time
 * - Memory usage impact
 * 
 * Results are saved to ./benchmarks/slash-commands-results.json
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const RESULTS_FILE = path.join(__dirname, 'slash-commands-results.json');
const CLI_PATH = path.join(__dirname, '..', 'bundle', 'gemini.js');

class SlashCommandsBenchmark {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      version: this.getVersion(),
      platform: process.platform,
      nodeVersion: process.version,
      metrics: {}
    };
  }

  getVersion() {
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
      return packageJson.version;
    } catch (error) {
      return 'unknown';
    }
  }

  async measureStartupTime() {
    console.log('Measuring CLI startup time with custom commands...');
    const measurements = [];
    
    // Take multiple measurements for accuracy
    for (let i = 0; i < 5; i++) {
      const startTime = Date.now();
      
      await new Promise((resolve) => {
        const child = spawn('node', [CLI_PATH, '--version'], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        child.on('close', () => {
          const duration = Date.now() - startTime;
          measurements.push(duration);
          resolve();
        });

        child.on('error', () => {
          measurements.push(Date.now() - startTime);
          resolve();
        });

        // Timeout after 10 seconds
        setTimeout(() => {
          child.kill();
          measurements.push(10000);
          resolve();
        }, 10000);
      });
    }

    const avgStartupTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
    const minStartupTime = Math.min(...measurements);
    const maxStartupTime = Math.max(...measurements);

    this.results.metrics.startupTime = {
      average_ms: Math.round(avgStartupTime),
      min_ms: minStartupTime,
      max_ms: maxStartupTime,
      measurements,
      target_ms: 150,
      meets_target: avgStartupTime <= 150
    };

    console.log(`Startup time: ${Math.round(avgStartupTime)}ms (avg), ${minStartupTime}ms (min), ${maxStartupTime}ms (max)`);
    console.log(`Target: ≤150ms - ${avgStartupTime <= 150 ? '✅ PASS' : '❌ FAIL'}`);
  }

  async testCustomCommandDiscovery() {
    console.log('Testing custom command discovery performance...');
    
    // Check if custom commands exist
    const commandsDir = path.join(__dirname, '..', '.gemini', 'commands');
    if (!fs.existsSync(commandsDir)) {
      console.log('No .gemini/commands directory found - skipping discovery test');
      this.results.metrics.customCommands = {
        available: false,
        reason: 'No .gemini/commands directory'
      };
      return;
    }

    // Count available commands
    const commandFiles = this.findCommandFiles(commandsDir);
    
    this.results.metrics.customCommands = {
      available: true,
      commandCount: commandFiles.length,
      commands: commandFiles.map(file => ({
        name: path.basename(file, '.md'),
        path: path.relative(commandsDir, file),
        size: fs.statSync(file).size
      }))
    };

    console.log(`Found ${commandFiles.length} custom command(s)`);
    commandFiles.forEach(file => {
      const relativePath = path.relative(commandsDir, file);
      console.log(`  - ${relativePath}`);
    });
  }

  findCommandFiles(dir) {
    const files = [];
    
    function scan(currentDir) {
      const items = fs.readdirSync(currentDir);
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scan(fullPath);
        } else if (item.endsWith('.md')) {
          files.push(fullPath);
        }
      }
    }
    
    try {
      scan(dir);
    } catch (error) {
      console.warn('Error scanning commands directory:', error.message);
    }
    
    return files;
  }

  async testCustomCommandExecution() {
    console.log('Testing custom command execution...');
    
    // This would require starting the interactive CLI, which is complex
    // For now, we'll just verify the commands can be read
    const commandsDir = path.join(__dirname, '..', '.gemini', 'commands');
    if (!fs.existsSync(commandsDir)) {
      this.results.metrics.execution = {
        tested: false,
        reason: 'No custom commands available'
      };
      return;
    }

    const commandFiles = this.findCommandFiles(commandsDir);
    const executionTests = [];

    for (const file of commandFiles) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        const hasArguments = content.includes('$ARGUMENT');
        
        executionTests.push({
          file: path.relative(commandsDir, file),
          contentLength: content.length,
          hasArguments,
          readable: true
        });
      } catch (error) {
        executionTests.push({
          file: path.relative(commandsDir, file),
          error: error.message,
          readable: false
        });
      }
    }

    this.results.metrics.execution = {
      tested: true,
      commands: executionTests,
      allReadable: executionTests.every(test => test.readable)
    };

    console.log(`Tested ${executionTests.length} command(s) for readability`);
    const readableCount = executionTests.filter(test => test.readable).length;
    console.log(`${readableCount}/${executionTests.length} commands are readable`);
  }

  async measureMemoryUsage() {
    console.log('Measuring memory usage...');
    const memUsage = process.memoryUsage();
    
    this.results.metrics.memoryUsage = {
      rss: memUsage.rss,
      heapTotal: memUsage.heapTotal,
      heapUsed: memUsage.heapUsed,
      external: memUsage.external,
      arrayBuffers: memUsage.arrayBuffers,
      rss_mb: Math.round(memUsage.rss / 1024 / 1024),
      heap_mb: Math.round(memUsage.heapUsed / 1024 / 1024)
    };

    console.log(`Memory usage - RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  }

  async run() {
    console.log('Starting slash commands benchmark...');
    console.log(`CLI Path: ${CLI_PATH}`);
    console.log(`Results will be saved to: ${RESULTS_FILE}`);
    
    // Check if CLI exists
    if (!fs.existsSync(CLI_PATH)) {
      console.error(`CLI not found at ${CLI_PATH}. Please run 'npm run build' first.`);
      process.exit(1);
    }

    try {
      await this.measureStartupTime();
      await this.testCustomCommandDiscovery();
      await this.testCustomCommandExecution();
      await this.measureMemoryUsage();

      // Add system info
      this.results.metrics.systemInfo = {
        cpuArch: process.arch,
        platform: process.platform,
        nodeVersion: process.version,
        timestamp: new Date().toISOString(),
        gitBranch: 'feature/1-slash-commands',
        buildStatus: 'successful'
      };

      // Save results
      fs.writeFileSync(RESULTS_FILE, JSON.stringify(this.results, null, 2));
      console.log(`\nSlash commands benchmark completed!`);
      console.log(`Results saved to: ${RESULTS_FILE}`);
      
      // Print summary
      this.printSummary();
      
      return this.results;
      
    } catch (error) {
      console.error('Benchmark failed:', error);
      this.results.error = error.message;
      fs.writeFileSync(RESULTS_FILE, JSON.stringify(this.results, null, 2));
      throw error;
    }
  }

  printSummary() {
    console.log('\n=== SLASH COMMANDS BENCHMARK SUMMARY ===');
    console.log(`Version: ${this.results.version}`);
    console.log(`Platform: ${this.results.platform}`);
    console.log(`Node.js: ${this.results.nodeVersion}`);
    console.log(`Timestamp: ${this.results.timestamp}`);
    console.log('\nMetrics:');
    
    if (this.results.metrics.startupTime) {
      const startup = this.results.metrics.startupTime;
      console.log(`  Startup Time: ${startup.average_ms}ms (avg) - ${startup.meets_target ? '✅ PASS' : '❌ FAIL'} (target: ≤${startup.target_ms}ms)`);
    }
    
    if (this.results.metrics.customCommands) {
      const commands = this.results.metrics.customCommands;
      if (commands.available) {
        console.log(`  Custom Commands: ${commands.commandCount} found`);
      } else {
        console.log(`  Custom Commands: Not available (${commands.reason})`);
      }
    }
    
    if (this.results.metrics.memoryUsage) {
      const mem = this.results.metrics.memoryUsage;
      console.log(`  Memory Usage: ${mem.rss_mb}MB RSS, ${mem.heap_mb}MB Heap`);
    }
    
    console.log('==========================================\n');
  }
}

// Run the benchmark if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const benchmark = new SlashCommandsBenchmark();
  benchmark.run().catch(console.error);
}

export default SlashCommandsBenchmark;
