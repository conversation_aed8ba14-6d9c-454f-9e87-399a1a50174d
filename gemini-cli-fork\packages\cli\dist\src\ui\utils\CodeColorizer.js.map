{"version": 3, "file": "CodeColorizer.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/CodeColorizer.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAQlD,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE1D,OAAO,EACL,WAAW,EACX,kBAAkB,GACnB,MAAM,qCAAqC,CAAC;AAE7C,2CAA2C;AAC3C,MAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;AAExC,SAAS,cAAc,CACrB,IAA6C,EAC7C,KAAY,EACZ,cAAkC;IAElC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QACzB,wDAAwD;QACxD,OAAO,KAAC,IAAI,IAAC,KAAK,EAAE,cAAc,YAAG,IAAI,CAAC,KAAK,GAAQ,CAAC;IAC1D,CAAC;IAED,qEAAqE;IACrE,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC5B,MAAM,WAAW,GACd,IAAI,CAAC,UAAU,EAAE,SAAsB,IAAI,EAAE,CAAC;QACjD,IAAI,YAAY,GAAuB,SAAS,CAAC;QAEjD,2DAA2D;QAC3D,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,GAAG,KAAK,CAAC;gBACrB,MAAM;YACR,CAAC;QACH,CAAC;QAED,sEAAsE;QACtE,0EAA0E;QAC1E,MAAM,eAAe,GAAG,YAAY,IAAI,cAAc,CAAC;QAEvD,iEAAiE;QACjE,+EAA+E;QAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,CACjC,CAAC,KAAqB,EAAE,KAAa,EAAE,EAAE,CAAC,CACxC,KAAC,KAAK,CAAC,QAAQ,cACZ,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,eAAe,CAAC,IAD3B,KAAK,CAET,CAClB,CACF,CAAC;QAEF,yEAAyE;QACzE,6DAA6D;QAC7D,OAAO,KAAC,KAAK,CAAC,QAAQ,cAAE,QAAQ,GAAkB,CAAC;IACrD,CAAC;IAED,iEAAiE;IACjE,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QACzB,+GAA+G;QAC/G,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,4EAA4E;QAC5E,4EAA4E;QAC5E,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,KAAkB,EAAE,KAAa,EAAE,EAAE,CAAC,CAC/D,KAAC,KAAK,CAAC,QAAQ,cACZ,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,cAAc,CAAC,IAD1B,KAAK,CAET,CAClB,CAAC,CAAC;IACL,CAAC;IAED,2CAA2C;IAC3C,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,YAAY,CAC1B,IAAY,EACZ,QAAuB,EACvB,eAAwB,EACxB,QAAiB;IAEjB,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM,WAAW,GAAG,YAAY,CAAC,cAAc,EAAE,CAAC;IAElD,IAAI,CAAC;QACH,+CAA+C;QAC/C,2EAA2E;QAC3E,IAAI,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,mDAAmD;QAEjG,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAEzB,8EAA8E;QAC9E,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAClC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;YAChE,IAAI,KAAK,CAAC,MAAM,GAAG,eAAe,EAAE,CAAC;gBACnC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC;gBAClD,gBAAgB,GAAG,UAAU,CAAC;gBAC9B,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,MAAM,mBAAmB,GAAG,CAAC,IAAY,EAAE,EAAE,CAC3C,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;YAC9B,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEzC,OAAO,CACL,KAAC,WAAW,IACV,SAAS,EAAE,eAAe,EAC1B,QAAQ,EAAE,QAAQ,EAClB,0BAA0B,EAAE,gBAAgB,EAC5C,iBAAiB,EAAC,KAAK,YAEtB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBACzB,MAAM,YAAY,GAAG,cAAc,CACjC,mBAAmB,CAAC,IAAI,CAAC,EACzB,WAAW,EACX,SAAS,CACV,CAAC;gBAEF,MAAM,eAAe,GAAG,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;gBACpE,OAAO,CACL,MAAC,GAAG,eACF,KAAC,IAAI,IAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,YACjC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAC9D,EACP,KAAC,IAAI,IAAC,KAAK,EAAE,WAAW,CAAC,YAAY,EAAE,IAAI,EAAC,MAAM,YAC/C,eAAe,GACX,KANC,KAAK,CAOT,CACP,CAAC;YACJ,CAAC,CAAC,GACU,CACf,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CACX,wDAAwD,QAAQ,IAAI,EACpE,KAAK,CACN,CAAC;QACF,qDAAqD;QACrD,wCAAwC;QACxC,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,mDAAmD;QACjG,OAAO,CACL,KAAC,WAAW,IACV,SAAS,EAAE,eAAe,EAC1B,QAAQ,EAAE,QAAQ,EAClB,iBAAiB,EAAC,KAAK,YAEtB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAC1B,MAAC,GAAG,eACF,KAAC,IAAI,IAAC,KAAK,EAAE,WAAW,CAAC,YAAY,YAClC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAC3C,EACP,KAAC,IAAI,IAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,YAAG,IAAI,GAAQ,KAJ3C,KAAK,CAKT,CACP,CAAC,GACU,CACf,CAAC;IACJ,CAAC;AACH,CAAC"}