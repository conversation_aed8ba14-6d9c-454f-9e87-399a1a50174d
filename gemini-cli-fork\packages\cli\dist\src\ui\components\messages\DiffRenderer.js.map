{"version": 3, "file": "DiffRenderer.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/messages/DiffRenderer.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AASvD,SAAS,wBAAwB,CAAC,WAAmB;IACnD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACtC,MAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,MAAM,eAAe,GAAG,iCAAiC,CAAC;IAE1D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC9C,IAAI,SAAS,EAAE,CAAC;YACd,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5C,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5C,MAAM,GAAG,IAAI,CAAC;YACd,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,wHAAwH;YACxH,kEAAkE;YAClE,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,SAAS;QACX,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,+CAA+C;YAC/C,IACE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;gBAC7B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACzB,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBACnC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;gBAC9B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;gBAC5B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;gBAChC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;gBAEpC,SAAS;YACX,sEAAsE;YACtE,SAAS;QACX,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,cAAc,EAAE,CAAC,CAAC,2BAA2B;YAC7C,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAC3B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,cAAc,EAAE,CAAC,CAAC,2BAA2B;YAC7C,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAC3B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,cAAc,EAAE,CAAC,CAAC,2BAA2B;YAC7C,cAAc,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAC3B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,uCAAuC;YACvC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAUD,MAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,mCAAmC;AAEhE,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EACxD,WAAW,EACX,QAAQ,EACR,QAAQ,GAAG,iBAAiB,EAC5B,uBAAuB,EACvB,aAAa,GACd,EAAE,EAAE;IACH,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;QACpD,OAAO,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,YAAY,iCAAyB,CAAC;IACnE,CAAC;IAED,MAAM,WAAW,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;IAE1D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,CACL,KAAC,GAAG,IAAC,WAAW,EAAC,OAAO,EAAC,WAAW,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,YAC3D,KAAC,IAAI,IAAC,QAAQ,2CAA4B,GACtC,CACP,CAAC;IACJ,CAAC;IAED,4EAA4E;IAC5E,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CACjC,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAC,IAAI,KAAK,KAAK;QACnB,IAAI,CAAC,IAAI,KAAK,MAAM;QACpB,IAAI,CAAC,IAAI,KAAK,OAAO;QACrB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAC3C,CAAC;IAEF,IAAI,cAAc,CAAC;IAEnB,IAAI,SAAS,EAAE,CAAC;QACd,wCAAwC;QACxC,MAAM,YAAY,GAAG,WAAW;aAC7B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC;aACrC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;aAC3B,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,gFAAgF;QAChF,MAAM,aAAa,GAAG,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC;QACzD,MAAM,QAAQ,GAAG,aAAa;YAC5B,CAAC,CAAC,wBAAwB,CAAC,aAAa,CAAC;YACzC,CAAC,CAAC,IAAI,CAAC;QACT,cAAc,GAAG,YAAY,CAC3B,YAAY,EACZ,QAAQ,EACR,uBAAuB,EACvB,aAAa,CACd,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,cAAc,GAAG,iBAAiB,CAChC,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,uBAAuB,EACvB,aAAa,CACd,CAAC;IACJ,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CACxB,WAAuB,EACvB,QAA4B,EAC5B,QAAQ,GAAG,iBAAiB,EAC5B,uBAA2C,EAC3C,aAAqB,EACrB,EAAE;IACF,iFAAiF;IACjF,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjD,GAAG,IAAI;QACP,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC3D,CAAC,CAAC,CAAC;IAEJ,0FAA0F;IAC1F,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAC7C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAC/C,CAAC;IAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,CACL,KAAC,GAAG,IAAC,WAAW,EAAC,OAAO,EAAC,WAAW,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,YAC3D,KAAC,IAAI,IAAC,QAAQ,2CAA4B,GACtC,CACP,CAAC;IACJ,CAAC;IAED,iEAAiE;IACjE,IAAI,eAAe,GAAG,QAAQ,CAAC,CAAC,iCAAiC;IACjE,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;QACpC,sEAAsE;QACtE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE;YAAE,SAAS;QAEzC,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,0CAA0C;QAC5F,MAAM,aAAa,GAAG,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,yCAAyC;QAC3G,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;IAC7D,CAAC;IACD,+FAA+F;IAC/F,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QAC/B,eAAe,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,MAAM,GAAG,GAAG,QAAQ;QAClB,CAAC,CAAC,YAAY,QAAQ,EAAE;QACxB,CAAC,CAAC,YAAY,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;IAE9F,IAAI,cAAc,GAAkB,IAAI,CAAC;IACzC,MAAM,6BAA6B,GAAG,CAAC,CAAC;IAExC,OAAO,CACL,KAAC,WAAW,IACV,SAAS,EAAE,uBAAuB,EAClC,QAAQ,EAAE,aAAa,YAGtB,gBAAgB,CAAC,MAAM,CAAoB,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/D,uEAAuE;YACvE,IAAI,4BAA4B,GAAkB,IAAI,CAAC;YACvD,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACnD,4BAA4B,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;YACtD,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAC/B,wFAAwF;gBACxF,4BAA4B,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;YACtD,CAAC;YAED,IACE,cAAc,KAAK,IAAI;gBACvB,4BAA4B,KAAK,IAAI;gBACrC,4BAA4B;oBAC1B,cAAc,GAAG,6BAA6B,GAAG,CAAC,EACpD,CAAC;gBACD,GAAG,CAAC,IAAI,CACN,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,IAAI,EAAC,UAAU,YAAE,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAQ,IADhD,OAAO,KAAK,EAAE,CAElB,CACP,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG,aAAa,KAAK,EAAE,CAAC;YACrC,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,IAAI,KAAK,GAAuB,SAAS,CAAC;YAC1C,IAAI,YAAY,GAAG,GAAG,CAAC;YACvB,IAAI,GAAG,GAAG,KAAK,CAAC;YAEhB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,KAAK;oBACR,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC/C,KAAK,GAAG,OAAO,CAAC;oBAChB,YAAY,GAAG,GAAG,CAAC;oBACnB,cAAc,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;oBACtC,MAAM;gBACR,KAAK,KAAK;oBACR,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC/C,KAAK,GAAG,KAAK,CAAC;oBACd,YAAY,GAAG,GAAG,CAAC;oBACnB,2EAA2E;oBAC3E,+EAA+E;oBAC/E,gFAAgF;oBAChF,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;wBAC/B,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC;oBAChC,CAAC;oBACD,MAAM;gBACR,KAAK,SAAS;oBACZ,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC/C,GAAG,GAAG,IAAI,CAAC;oBACX,YAAY,GAAG,GAAG,CAAC;oBACnB,cAAc,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;oBACtC,MAAM;gBACR;oBACE,OAAO,GAAG,CAAC;YACf,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAE/D,GAAG,CAAC,IAAI,CACN,MAAC,GAAG,IAAe,aAAa,EAAC,KAAK,aACpC,MAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,IAAI,aAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAC1D,MAAC,IAAI,IAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,aAC9B,YAAY,EAAE,GAAG,IACb,EACP,KAAC,IAAI,IAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAC,MAAM,YAC3C,cAAc,GACV,KAPC,OAAO,CAQX,CACP,CAAC;YACF,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,IAzED,GAAG,CA0EI,CACf,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,wBAAwB,GAAG,CAAC,SAAiB,EAAiB,EAAE;IACpE,MAAM,WAAW,GAA8B;QAC7C,EAAE,EAAE,YAAY;QAChB,EAAE,EAAE,YAAY;QAChB,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,MAAM;QACZ,GAAG,EAAE,KAAK;QACV,IAAI,EAAE,MAAM;QACZ,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,UAAU;QACd,IAAI,EAAE,MAAM;QACZ,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,WAAW;QAChB,IAAI,EAAE,MAAM;QACZ,CAAC,EAAE,GAAG;QACN,GAAG,EAAE,KAAK;QACV,EAAE,EAAE,MAAM;KACX,CAAC;IACF,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,CAAC,qCAAqC;AAC9E,CAAC,CAAC"}