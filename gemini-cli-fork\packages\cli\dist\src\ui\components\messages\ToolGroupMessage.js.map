{"version": 3, "file": "ToolGroupMessage.js", "sourceRoot": "", "sources": ["../../../../../src/ui/components/messages/ToolGroupMessage.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AACvC,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC1B,OAAO,EAA6B,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAC3E,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAYzC,yEAAyE;AACzE,MAAM,CAAC,MAAM,gBAAgB,GAAoC,CAAC,EAChE,SAAS,EACT,uBAAuB,EACvB,aAAa,EACb,MAAM,EACN,SAAS,GAAG,IAAI,GACjB,EAAE,EAAE;IACH,MAAM,UAAU,GAAG,CAAC,SAAS,CAAC,KAAK,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc,CAAC,OAAO,CAC3C,CAAC;IACF,MAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;IAEnE,MAAM,YAAY,GAAG,YAAY,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;IAC3D,sEAAsE;IACtE,cAAc;IACd,MAAM,UAAU,GAAG,aAAa,GAAG,CAAC,CAAC;IAErC,2EAA2E;IAC3E,mFAAmF;IACnF,MAAM,oBAAoB,GAAG,OAAO,CAClC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,KAAK,cAAc,CAAC,UAAU,CAAC,EACrE,CAAC,SAAS,CAAC,CACZ,CAAC;IAEF,IAAI,yBAAyB,GAAG,CAAC,CAAC;IAClC,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,EAAE,EAAE,CAAC;YAClE,yBAAyB,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IACD,MAAM,qBAAqB,GAAG,SAAS,CAAC,MAAM,GAAG,yBAAyB,CAAC;IAC3E,MAAM,qCAAqC,GAAG,uBAAuB;QACnE,CAAC,CAAC,IAAI,CAAC,GAAG,CACN,IAAI,CAAC,KAAK,CACR,CAAC,uBAAuB,GAAG,YAAY,GAAG,qBAAqB,CAAC;YAC9D,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC,CACzC,EACD,CAAC,CACF;QACH,CAAC,CAAC,SAAS,CAAC;IAEd,OAAO,CACL,KAAC,GAAG,IACF,aAAa,EAAC,QAAQ,EACtB,WAAW,EAAC,OAAO;QACnB;;;;;UAKE;QACF,KAAK,EAAC,MAAM,EACZ,UAAU,EAAE,CAAC,EACb,cAAc,EAAE,UAAU,EAC1B,WAAW,EAAE,WAAW,YAEvB,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACtB,MAAM,YAAY,GAAG,oBAAoB,EAAE,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC;YAClE,OAAO,CACL,MAAC,GAAG,IAAmB,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,aACxD,KAAC,GAAG,IAAC,aAAa,EAAC,KAAK,EAAC,UAAU,EAAC,QAAQ,YAC1C,KAAC,WAAW,IACV,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAC7C,uBAAuB,EAAE,qCAAqC,EAC9D,aAAa,EAAE,UAAU,EACzB,QAAQ,EACN,YAAY;gCACV,CAAC,CAAC,MAAM;gCACR,CAAC,CAAC,oBAAoB;oCACpB,CAAC,CAAC,KAAK;oCACP,CAAC,CAAC,QAAQ,EAEhB,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,GACnD,GACE,EACL,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,UAAU;wBACxC,YAAY;wBACZ,IAAI,CAAC,mBAAmB,IAAI,CAC1B,KAAC,uBAAuB,IACtB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAC7C,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,SAAS,EACpB,uBAAuB,EACrB,qCAAqC,EAEvC,aAAa,EAAE,UAAU,GACzB,CACH,KAjCK,IAAI,CAAC,MAAM,CAkCf,CACP,CAAC;QACJ,CAAC,CAAC,GACE,CACP,CAAC;AACJ,CAAC,CAAC"}