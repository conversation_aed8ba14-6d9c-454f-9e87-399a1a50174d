{"version": 3, "file": "github-dark.js", "sourceRoot": "", "sources": ["../../../../src/ui/themes/github-dark.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAoB,KAAK,EAAE,MAAM,YAAY,CAAC;AAErD,MAAM,gBAAgB,GAAgB;IACpC,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,SAAS;IACrB,UAAU,EAAE,SAAS;IACrB,SAAS,EAAE,SAAS;IACpB,UAAU,EAAE,SAAS;IACrB,YAAY,EAAE,SAAS;IACvB,UAAU,EAAE,SAAS;IACrB,WAAW,EAAE,SAAS;IACtB,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,SAAS;IACpB,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,SAAS;IACf,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;CACvC,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAU,IAAI,KAAK,CACxC,QAAQ,EACR,MAAM,EACN;IACE,IAAI,EAAE;QACJ,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,gBAAgB,CAAC,UAAU;QAClC,UAAU,EAAE,gBAAgB,CAAC,UAAU;KACxC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,gBAAgB,CAAC,OAAO;QAC/B,SAAS,EAAE,QAAQ;KACpB;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,gBAAgB,CAAC,OAAO;QAC/B,SAAS,EAAE,QAAQ;KACpB;IACD,cAAc,EAAE;QACd,KAAK,EAAE,gBAAgB,CAAC,SAAS;QACjC,UAAU,EAAE,MAAM;KACnB;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,gBAAgB,CAAC,SAAS;QACjC,UAAU,EAAE,MAAM;KACnB;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,gBAAgB,CAAC,UAAU;KACnC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,gBAAgB,CAAC,SAAS;KAClC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,gBAAgB,CAAC,SAAS;KAClC;IACD,eAAe,EAAE;QACf,KAAK,EAAE,gBAAgB,CAAC,YAAY;KACrC;IACD,wBAAwB,EAAE;QACxB,KAAK,EAAE,gBAAgB,CAAC,YAAY;KACrC;IACD,qBAAqB,EAAE;QACrB,KAAK,EAAE,gBAAgB,CAAC,YAAY;KACrC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,gBAAgB,CAAC,UAAU;KACnC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,gBAAgB,CAAC,UAAU;KACnC;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,gBAAgB,CAAC,YAAY;QACpC,UAAU,EAAE,MAAM;KACnB;IACD,cAAc,EAAE;QACd,KAAK,EAAE,gBAAgB,CAAC,YAAY;QACpC,UAAU,EAAE,MAAM;KACnB;IACD,kBAAkB,EAAE;QAClB,KAAK,EAAE,gBAAgB,CAAC,YAAY;QACpC,UAAU,EAAE,MAAM;KACnB;IACD,WAAW,EAAE;QACX,KAAK,EAAE,gBAAgB,CAAC,WAAW;QACnC,UAAU,EAAE,MAAM;KACnB;IACD,wBAAwB,EAAE;QACxB,KAAK,EAAE,gBAAgB,CAAC,WAAW;QACnC,UAAU,EAAE,MAAM;KACnB;IACD,UAAU,EAAE;QACV,KAAK,EAAE,gBAAgB,CAAC,WAAW;KACpC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,gBAAgB,CAAC,WAAW;KACpC;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,gBAAgB,CAAC,SAAS;KAClC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,gBAAgB,CAAC,UAAU;KACnC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,gBAAgB,CAAC,UAAU;KACnC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,gBAAgB,CAAC,YAAY;KACrC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,gBAAgB,CAAC,YAAY;KACrC;IACD,eAAe,EAAE;QACf,KAAK,EAAE,gBAAgB,CAAC,SAAS;KAClC;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,gBAAgB,CAAC,SAAS;KAClC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,gBAAgB,CAAC,SAAS;QACjC,UAAU,EAAE,MAAM;KACnB;IACD,eAAe,EAAE;QACf,UAAU,EAAE,SAAS;QACrB,KAAK,EAAE,gBAAgB,CAAC,SAAS;KAClC;IACD,eAAe,EAAE;QACf,UAAU,EAAE,SAAS;QACrB,KAAK,EAAE,gBAAgB,CAAC,WAAW;KACpC;IACD,eAAe,EAAE;QACf,SAAS,EAAE,QAAQ;KACpB;IACD,aAAa,EAAE;QACb,UAAU,EAAE,MAAM;KACnB;CACF,EACD,gBAAgB,CACjB,CAAC"}