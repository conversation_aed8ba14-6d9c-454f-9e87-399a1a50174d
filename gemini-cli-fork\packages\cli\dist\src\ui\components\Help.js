import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const Help = ({ commands }) => (_jsxs(Box, { flexDirection: "column", marginBottom: 1, borderColor: Colors.Gray, borderStyle: "round", padding: 1, children: [_jsx(Text, { bold: true, color: Colors.Foreground, children: "Basics:" }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Add context" }), ": Use", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "@" }), ' ', "to specify files for context (e.g.,", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "@src/myFile.ts" }), ") to target specific files or folders."] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Shell mode" }), ": Execute shell commands via", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "!" }), ' ', "(e.g.,", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "!npm run start" }), ") or use natural language (e.g.", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "start server" }), ")."] }), _jsx(Box, { height: 1 }), _jsx(Text, { bold: true, color: Colors.Foreground, children: "Commands:" }), commands
            .filter((command) => command.description)
            .map((command) => (_jsxs(Text, { color: Colors.Foreground, children: [_jsxs(Text, { bold: true, color: Colors.AccentPurple, children: [' ', "/", command.name] }), command.description && ' - ' + command.description] }, command.name))), _jsxs(Text, { color: Colors.Foreground, children: [_jsxs(Text, { bold: true, color: Colors.AccentPurple, children: [' ', "!", ' '] }), "- shell command"] }), _jsx(Box, { height: 1 }), _jsx(Text, { bold: true, color: Colors.Foreground, children: "Keyboard Shortcuts:" }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Enter" }), ' ', "- Send message"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Shift+Enter" }), ' ', "- New line"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Up/Down" }), ' ', "- Cycle through your prompt history"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Alt+Left/Right" }), ' ', "- Jump through words in the input"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Esc" }), ' ', "- Cancel operation"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { bold: true, color: Colors.AccentPurple, children: "Ctrl+C" }), ' ', "- Quit application"] })] }));
//# sourceMappingURL=Help.js.map