import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const UpdateNotification = ({ message }) => (_jsx(Box, { borderStyle: "round", borderColor: Colors.AccentYellow, paddingX: 1, marginY: 1, children: _jsx(Text, { color: Colors.AccentYellow, children: message }) }));
//# sourceMappingURL=UpdateNotification.js.map